import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { readGig, updateGig, readAllGigs } from '@/lib/gigs/hierarchical-storage';
import { saveProject } from '@/lib/projects-utils';
import { readFile, writeFile } from 'fs/promises';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const requestId = params.id;
    const body = await request.json();

    // Handle both regular request IDs and targeted gig IDs
    const isTargetedRequest = requestId.startsWith('targeted_');
    let gigRequest: any = null;
    let gigId: number | null = null;

    if (isTargetedRequest) {
      // Handle targeted gig requests
      const actualGigId = parseInt(requestId.replace('targeted_', ''));
      if (isNaN(actualGigId)) {
        return NextResponse.json(
          { error: 'Invalid targeted gig ID' },
          { status: 400 }
        );
      }

      // Get the targeted gig
      const gig = await readGig(actualGigId);
      if (!gig || !gig.isTargetedRequest) {
        return NextResponse.json(
          { error: 'Targeted gig not found' },
          { status: 404 }
        );
      }

      gigId = actualGigId;
      gigRequest = {
        id: requestId,
        gigId: actualGigId,
        freelancerId: gig.targetFreelancerId,
        commissionerId: gig.commissionerId,
        organizationId: gig.organizationId,
        title: gig.title,
        status: 'accepted'
      };
    } else {
      // Handle regular gig requests
      const numericRequestId = parseInt(requestId);
      if (isNaN(numericRequestId)) {
        return NextResponse.json(
          { error: 'Invalid request ID' },
          { status: 400 }
        );
      }

      // Read gig requests data from hierarchical storage
      const { readAllGigRequests } = await import('../../../../../lib/gigs/gig-request-storage');
      const requestsData = await readAllGigRequests();

      // Find the specific request
      const requestIndex = requestsData.findIndex((r: any) => r.id === numericRequestId);

      if (requestIndex === -1) {
        return NextResponse.json(
          { error: 'Gig request not found' },
          { status: 404 }
        );
      }

      gigRequest = requestsData[requestIndex];
      gigId = gigRequest.gigId;
    }

    // Check if project already exists for this gig to prevent duplicates
    const existingProjectsPath = path.join(process.cwd(), 'data', 'projects');
    const projectsData = await import('@/lib/projects-utils').then(m => m.readAllProjects());
    const existingProject = projectsData.find((p: any) => p.gigId === gigId);

    if (existingProject) {
      return NextResponse.json(
        { error: 'Project already exists for this gig' },
        { status: 409 }
      );
    }

    // Read additional data for project creation
    const [organizationsData, usersData] = await Promise.all([
      readFile(path.join(process.cwd(), 'data/organizations.json'), 'utf-8').then(data => JSON.parse(data)),
      readFile(path.join(process.cwd(), 'data/users.json'), 'utf-8').then(data => JSON.parse(data))
    ]);

    // Find organization and manager
    const organization = organizationsData.find((org: any) => org.id === gigRequest.organizationId);
    const manager = usersData.find((user: any) => user.id === organization?.contactPersonId);

    if (!organization || !manager) {
      return NextResponse.json(
        { error: 'Organization or manager not found' },
        { status: 404 }
      );
    }

    // Get gig details for project creation (if exists)
    const gig = await readGig(gigId!);

    // For standalone gig requests without corresponding gigs, create minimal gig data
    const gigData = gig || {
      title: gigRequest.title,
      description: gigRequest.notes || `Project for ${gigRequest.title}`,
      tags: gigRequest.skills || [],
      deliveryTimeWeeks: 4, // Default delivery time
      status: 'Available'
    };

    // Generate new project ID
    const maxProjectId = Math.max(...projectsData.map((p: any) => p.projectId), 0);
    const newProjectId = maxProjectId + 1;

    // Create new project
    const newProject = {
      projectId: newProjectId,
      title: gigRequest.title || gigData.title,
      description: gigData.description,
      organizationId: gigRequest.organizationId,
      typeTags: gigData.tags || [],
      manager: {
        name: manager.name,
        title: manager.title,
        avatar: manager.avatar,
        email: manager.email
      },
      commissionerId: gigRequest.commissionerId,
      freelancerId: gigRequest.freelancerId,
      status: 'ongoing',
      dueDate: new Date(Date.now() + (gigData.deliveryTimeWeeks || 4) * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      totalTasks: 1,
      gigId: gigId, // Link to original gig (may be null for standalone requests)
      createdAt: new Date().toISOString()
    };

    // Save project
    await saveProject(newProject);

    // Update gig status to unavailable (only if gig exists)
    if (gig) {
      await updateGig(gigId!, { status: 'Unavailable' });
    }

    // Update request status for regular requests
    if (!isTargetedRequest) {
      // TODO: Implement hierarchical update for gig request status
      // For now, we'll skip this update since the data is now in hierarchical structure
      // This functionality should be implemented in a future update
      console.log(`Gig request ${requestId} accepted, but status update skipped (hierarchical storage)`);
    }

    // Create commissioner notification
    await createCommissionerNotification(gigRequest, newProjectId);

    return NextResponse.json({
      success: true,
      message: 'Gig request accepted successfully and project created',
      projectId: newProjectId,
      request: gigRequest
    });
  } catch (error) {
    console.error('Error accepting gig request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to create commissioner notification
async function createCommissionerNotification(gigRequest: any, projectId: number) {
  try {
    // Read freelancer data to get name
    const usersData = await readFile(path.join(process.cwd(), 'data/users.json'), 'utf-8');
    const users = JSON.parse(usersData);
    const freelancersData = await readFile(path.join(process.cwd(), 'data/freelancers.json'), 'utf-8');
    const freelancers = JSON.parse(freelancersData);

    const freelancer = freelancers.find((f: any) => f.id === gigRequest.freelancerId);
    const freelancerUser = users.find((u: any) => u.id === freelancer?.userId);

    if (!freelancerUser) {
      console.error('Freelancer user not found for notification');
      return;
    }

    // Create notification entry
    const notification = {
      id: Date.now(), // Simple ID generation
      type: 7, // Type for gig acceptance
      actorId: gigRequest.freelancerId,
      targetId: gigRequest.commissionerId,
      entityId: projectId,
      entityType: 'project',
      message: `${freelancerUser.name} has accepted your gig request for "${gigRequest.title}". A new project has been created.`,
      timestamp: new Date().toISOString(),
      read: false,
      metadata: {
        gigId: gigRequest.gigId,
        projectId: projectId,
        freelancerName: freelancerUser.name,
        gigTitle: gigRequest.title
      }
    };

    // Read existing notifications
    const notificationsPath = path.join(process.cwd(), 'data/notifications/notifications-log.json');
    let notifications = [];
    try {
      const notificationsData = await readFile(notificationsPath, 'utf-8');
      notifications = JSON.parse(notificationsData);
    } catch (error) {
      // File doesn't exist, start with empty array
      notifications = [];
    }

    // Add new notification
    notifications.push(notification);

    // Write back to file
    await writeFile(notificationsPath, JSON.stringify(notifications, null, 2));

    console.log('Commissioner notification created successfully');
  } catch (error) {
    console.error('Error creating commissioner notification:', error);
  }
}
